"use client";
import React, { useState, useEffect } from "react";

import { UserManagementViewProps, User } from "@/types/userTypes";
import { useAuthStore } from "@/store/authStore";
import { useRoleAccess } from "@/hooks/useRoleAccess";
import SearchAndFilters from "./SearchAndFilters";
import UserTable from "./UserTable";
import RemoveUserModal from "../RemoveUserModal";

const UserManagementView: React.FC<UserManagementViewProps> = ({
  users,
  pendingInvitations,
  pendingInvitationsList,
  onAddUser,
  onEditUser,
  onRemoveUser,
  onRevokeInvitation,
  onResendInvitation,
  loading,
  isAdmin,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredUsers, setFilteredUsers] = useState<User[]>(users);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const [isUserTypeDropdownOpen, setIsUserTypeDropdownOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [userToRemove, setUserToRemove] = useState<User | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Filter states
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [selectedUserType, setSelectedUserType] = useState<string>("");
  const [selectedDateSort, setSelectedDateSort] = useState<string>("");
  const { formData, isAuthenticated } = useAuthStore();
  const { userRole } = useRoleAccess();

  // Handle client-side mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Get current user information from localStorage and auth store (client-side only)
  useEffect(() => {
    if (!isMounted) return; // Only run on client-side after mounting

    const getCurrentUser = () => {
      if (isAuthenticated && typeof window !== 'undefined') {
        // Try to get email from localStorage first (persisted)
        let userEmail = localStorage.getItem("userEmail");
        let userName = localStorage.getItem("userName");

        // If not in localStorage, try to get from auth store formData
        if (!userEmail && formData.email) {
          userEmail = formData.email;
          userName = formData.email.split("@")[0]; // Use part before @ as name

          // Persist to localStorage for future use
          localStorage.setItem("userEmail", userEmail);
          localStorage.setItem("userName", userName);
        }

        // Also save to localStorage if formData has email (for when user just logged in)
        if (formData.email && formData.email !== userEmail) {
          userEmail = formData.email;
          userName = formData.email.split("@")[0];
          localStorage.setItem("userEmail", userEmail);
          localStorage.setItem("userName", userName);
        }

        // Create current user object with actual role from auth store
        const currentDate = new Date();
        const currentUserRole = userRole === "admin" ? "Admin" :
                              userRole === "security" ? "Security" : "User";

        const currentUserObj: User = {
          id: "current-user",
          name: userName || "Current User",
          email: userEmail || "<EMAIL>",
          role: currentUserRole,
          status: "Active",
          dateAdded: currentDate.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          }),
        };

        console.log("Current user:", currentUserObj);
        setCurrentUser(currentUserObj);
      }
    };

    getCurrentUser();
  }, [isMounted, isAuthenticated, formData.email, userRole]);

  // Filter users based on search query and filters (exclude pending users from main table)
  useEffect(() => {
    let filtered = users.filter(user => user.status !== "Pending");

    // Apply search filter
    if (searchQuery.trim() !== "") {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.role.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (selectedStatus && selectedStatus !== "") {
      filtered = filtered.filter(user => user.status === selectedStatus);
    }

    // Apply user type filter
    if (selectedUserType && selectedUserType !== "") {
      filtered = filtered.filter(user => user.role === selectedUserType);
    }

    // Apply date sorting
    if (selectedDateSort && selectedDateSort !== "") {
      filtered = [...filtered].sort((a, b) => {
        const dateA = new Date(a.dateAdded);
        const dateB = new Date(b.dateAdded);
        
        if (selectedDateSort === "Newest First") {
          return dateB.getTime() - dateA.getTime();
        } else if (selectedDateSort === "Oldest First") {
          return dateA.getTime() - dateB.getTime();
        }
        return 0;
      });
    }

    setFilteredUsers(filtered);
  }, [searchQuery, users, selectedStatus, selectedUserType, selectedDateSort]);

  const handleUserSelect = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map((user) => user.id));
    }
  };

  const handleEditUser = (user: User) => {
    onEditUser(user);
  };

  const handleRemoveUser = (user: User) => {
    setUserToRemove(user);
    setIsRemoveModalOpen(true);
  };

  const handleConfirmRemove = (userId: string) => {
    if (userId === "current-user") {
      console.error("Attempted to remove current admin user - this should not happen!");
      return;
    }
    onRemoveUser(userId);
    setIsRemoveModalOpen(false);
    setUserToRemove(null);
  };

  return (
    <div className="text-white lg:p-6 py-6">
      <SearchAndFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        pendingInvitations={pendingInvitations}
        pendingInvitationsList={pendingInvitationsList || []}
        onAddUser={onAddUser}
        isAuthenticated={isMounted && isAuthenticated}
        isAdmin={isAdmin}
        onRevokeInvitation={onRevokeInvitation}
        onResendInvitation={onResendInvitation}
        loading={loading}
        selectedStatus={selectedStatus}
        selectedUserType={selectedUserType}
        selectedDateSort={selectedDateSort}
        isDateDropdownOpen={isDateDropdownOpen}
        isStatusDropdownOpen={isStatusDropdownOpen}
        isUserTypeDropdownOpen={isUserTypeDropdownOpen}
        onStatusSelect={setSelectedStatus}
        onUserTypeSelect={setSelectedUserType}
        onDateSortSelect={setSelectedDateSort}
        onToggleDateDropdown={() => setIsDateDropdownOpen(!isDateDropdownOpen)}
        onToggleStatusDropdown={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
        onToggleUserTypeDropdown={() => setIsUserTypeDropdownOpen(!isUserTypeDropdownOpen)}
      />

      <UserTable
        currentUser={currentUser}
        filteredUsers={filteredUsers}
        selectedUsers={selectedUsers}
        searchQuery={searchQuery}
        onSelectAll={handleSelectAll}
        onUserSelect={handleUserSelect}
        onEditUser={handleEditUser}
        onRemoveUser={handleRemoveUser}
        isAdmin={isAdmin}
      />

      {/* Remove User Modal */}
      <RemoveUserModal
        isOpen={isRemoveModalOpen}
        onClose={() => setIsRemoveModalOpen(false)}
        user={userToRemove}
        onConfirmRemove={handleConfirmRemove}
      />
    </div>
  );
};

export default UserManagementView;
