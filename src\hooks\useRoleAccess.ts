import { useAuthStore } from "@/store/authStore";
import { useState, useEffect } from "react";
import { authServices } from "@/utils/authServices";
import Cookies from "js-cookie";

export const useRoleAccess = () => {
  const { userRole, isAuthenticated, setUserRole } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);

  // Debug logging
  useEffect(() => {
    console.log("useRoleAccess - isAuthenticated:", isAuthenticated, "userRole:", userRole);
    console.log("useRoleAccess - userRole cookie:", Cookies.get("userRole"));
  }, [isAuthenticated, userRole]);

  // Fetch user role from backend if missing - but be very conservative about it
  useEffect(() => {
    const fetchUserRole = async () => {
      // Only fetch role if authenticated but no role is set AND no role in cookies
      const cookieRole = Cookies.get("userRole");

      if (isAuthenticated && !userRole && !cookieRole) {
        try {
          const authToken = Cookies.get("authToken");
          if (authToken) {
            console.log("Fetching user role from backend (no role in state or cookies)...");
            const userData = await authServices.getUser(authToken);
            const role = userData.result?.role || userData.user?.role || userData.role;
            console.log("Fetched user role from backend:", role);

            // Only set the role if we actually got a valid role from the backend
            if (role && typeof role === 'string') {
              console.log("Setting user role from backend:", role);
              setUserRole(role);
            } else {
              console.log("No valid role received from backend, keeping current role");
            }
          }
        } catch (error: unknown) {
          const err = error as { response?: { status?: number } };
          console.error("Failed to fetch user role:", error);

          // Don't show toast errors for 403 (permission denied) when fetching user role
          // This is expected behavior when users don't have access to certain endpoints
          if (err.response?.status === 403) {
            console.log("Access denied (403) when fetching user role - this is expected for restricted endpoints");
            // Don't clear the role on 403 - user might have a valid role already stored
            return;
          }

          // For other errors, only clear role if we don't have one already
          if (!userRole && !cookieRole) {
            console.log("Failed to fetch user role and no existing role - setting to null for security");
            setUserRole(null);
          } else {
            console.log("Failed to fetch user role but keeping existing role:", userRole || cookieRole);
          }
        }
      } else if (isAuthenticated && !userRole && cookieRole) {
        // If we have a role in cookies but not in state, restore it
        console.log("Restoring user role from cookie:", cookieRole);
        setUserRole(cookieRole);
      }
    };

    fetchUserRole();
  }, [isAuthenticated, userRole, setUserRole]);

  // Handle loading state - stop loading when we have auth state or after timeout
  useEffect(() => {
    // If we have both auth status and role (or confirmed not authenticated), stop loading
    if ((isAuthenticated && userRole) || !isAuthenticated) {
      console.log("Stopping loading - auth state determined");
      setIsLoading(false);
      return;
    }

    // Otherwise, set a timeout to stop loading after a reasonable delay
    const timer = setTimeout(() => {
      console.log("Stopping loading - timeout reached");
      setIsLoading(false);
    }, 2000); // Give more time for API call to complete

    return () => clearTimeout(timer);
  }, [isAuthenticated, userRole]);

  const isAdmin = () => {
    return isAuthenticated && userRole === "admin";
  };

  const isUser = () => {
    return isAuthenticated && userRole === "user";
  };

  const isSecurity = () => {
    return isAuthenticated && userRole === "security";
  };

  const isAudit = () => {
    return isAuthenticated && userRole === "audit";
  };

  const hasRole = (role: string) => {
    return isAuthenticated && userRole === role;
  };

  const canAccessUserManagement = () => {
    return isAuthenticated; // Allow all authenticated users to access user management
  };

  return {
    userRole,
    isAdmin: isAdmin(),
    isUser: isUser(),
    isSecurity: isSecurity(),
    isAudit: isAudit(),
    hasRole,
    canAccessUserManagement: canAccessUserManagement(),
    isLoading,
  };
};
