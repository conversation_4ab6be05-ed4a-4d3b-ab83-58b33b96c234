import { create } from "zustand";
import { AuthStore } from "@/types/authtypes";
import Cookies from "js-cookie";

export const useAuthStore = create<AuthStore>((set) => ({
  activeTab: "Sign Up",
  setActiveTab: (tab) => set({ activeTab: tab }),

  showPassword: false,
  setShowPassword: (value) => set({ showPassword: value }),

  showConfirmPassword: false,
  setShowConfirmPassword: (value) => set({ showConfirmPassword: value }),

  formData: {
    email: "",
    password: "",
    confirmPassword: "",
    name: "",
    companyName: "",
  },
  setFormData: (data) =>
    set((state) => ({
      formData: { ...state.formData, ...data },
    })),

  errors: {
    email: "",
    password: "",
    confirmPassword: "",
    name: "",
    companyName: "",
  },
  setErrors: (errorsOrUpdater) =>
    set((state) => ({
      errors:
        typeof errorsOrUpdater === "function"
          ? errorsOrUpdater(state.errors)
          : errorsOrUpdater,
    })),

  focusedField: null,
  setFocusedField: (field) => set({ focusedField: field }),

  // Forgot Password states
  forgotPasswordStep: 1,
  setForgotPasswordStep: (step) => set({ forgotPasswordStep: step }),

  otp: ["", "", "", "", ""],
  setOtp: (otp) => set({ otp }),

  otpSent: false,
  setOtpSent: (value) => set({ otpSent: value }),

  otpError: "",
  setOtpError: (error) => set({ otpError: error }),

  // Submit attempt tracking
  submitAttempted: false,
  setSubmitAttempted: (value) => set({ submitAttempted: value }),

  // Authentication status
  isAuthenticated: !!Cookies.get("authToken"),
  setIsAuthenticated: (value) => set({ isAuthenticated: value }),

  // API response data
  token: Cookies.get("authToken") || null,
  setToken: (token) => {
    if (token) {
      // Remove httpOnly: true as it's not supported on client-side
      Cookies.set("authToken", token, {
        expires: 1 / 24,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });
    } else {
      Cookies.remove("authToken");
    }
    set({ token });
  },

  userId: null,
  setUserId: (userId) => set({ userId }),

  // User role - initialize from cookies if available
  userRole: (() => {
    const role = Cookies.get("userRole");
    console.log("Initializing userRole from cookie:", role);
    return role || null;
  })(),
  setUserRole: (role) => {
    console.log("Setting user role:", role, "current role:", get().userRole);

    // If we already have a valid role and the new role is null/undefined, don't override
    const currentRole = get().userRole;
    if (currentRole && !role) {
      console.log("Preventing role override - keeping existing role:", currentRole);
      return;
    }

    if (role) {
      Cookies.set("userRole", role, {
        expires: 1 / 24, // Same expiry as auth token
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });
      console.log("User role cookie set:", Cookies.get("userRole"));
    } else {
      Cookies.remove("userRole");
      console.log("User role cookie removed");
    }
    set({ userRole: role });
  },

  // Invitation data
  invitationData: null,
  setInvitationData: (data) => set({ invitationData: data }),

  // Logout action
  clearAuth: () => {
    Cookies.remove("authToken");
    Cookies.remove("refreshToken");
    Cookies.remove("userRole");
    // Also clear localStorage
    localStorage.removeItem("userEmail");
    localStorage.removeItem("userName");
    set({
      isAuthenticated: false,
      token: null,
      userId: null,
      userRole: null,
      formData: { email: "", password: "", confirmPassword: "", name: "", companyName: "" },
      errors: { email: "", password: "", confirmPassword: "", name: "", companyName: "" },
      invitationData: null,
    });
  },
}));