"use client";
import React from "react";
import { User } from "@/types/userTypes";
import UserTableHeader from "./UserTableHeader";
import UserTableRow from "./UserTableRow";

interface UserTableProps {
  currentUser: User | null;
  filteredUsers: User[];
  selectedUsers: string[];
  searchQuery: string;
  onSelectAll: () => void;
  onUserSelect: (userId: string) => void;
  onEditUser: (user: User) => void;
  onRemoveUser: (user: User) => void;
  isAdmin: boolean;
}

const UserTable: React.FC<UserTableProps> = ({
  currentUser,
  filteredUsers,
  selectedUsers,
  searchQuery,
  onSelectAll,
  onUserSelect,
  onEditUser,
  onRemoveUser,
  isAdmin,
}) => {
  return (
    <div className="border border-[#242424] overflow-hidden">
      {/* Table Header */}
      <UserTableHeader
        selectedCount={selectedUsers.length}
        totalCount={filteredUsers.length}
        onSelectAll={onSelectAll}
      />

      {/* Table Body - Scrollable */}
      <div
        className="divide-y divide-[#3D3D3D] overflow-y-auto scrollbar-thin scrollbar-track-[#1F1F1F] scrollbar-thumb-[#3D3D3D] hover:scrollbar-thumb-[#4D4D4D]"
        style={{ height: 'calc(min(500px, 70vh))' }}
      >
        {/* Current Admin User - Always shown first */}
        {currentUser && (
          <UserTableRow
            user={currentUser}
            isSelected={false}
            onSelect={() => {}}
            onEditUser={onEditUser}
            onRemoveUser={onRemoveUser}
            isCurrentAdmin={true}
            isViewerAdmin={isAdmin}
          />
        )}

        {/* Other Users */}
        {filteredUsers.length > 0
          ? filteredUsers.map((user) => (
              <UserTableRow
                key={user.id}
                user={user}
                isSelected={selectedUsers.includes(user.id)}
                onSelect={onUserSelect}
                onEditUser={onEditUser}
                onRemoveUser={onRemoveUser}
                isViewerAdmin={isAdmin}
              />
            ))
          : !currentUser && (
              <div className="px-6 py-8 text-center">
                <p className="text-[#A3A3A3]">
                  {searchQuery
                    ? "No users found matching your search."
                    : "No users added yet."}
                </p>
              </div>
            )}
      </div>
    </div>
  );
};

export default UserTable;
