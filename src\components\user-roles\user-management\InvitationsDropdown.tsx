"use client";
import React, { useState } from "react";
import { ApiInvitation } from "@/utils/userServices";
import { IoMdClose } from "react-icons/io";
import { HiOutlineRefresh } from "react-icons/hi";
import { MdDeleteOutline } from "react-icons/md";

interface InvitationsDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  invitations: ApiInvitation[];
  onRevoke: (invitationId: string) => Promise<boolean>;
  onResend: (invitationId: string) => Promise<boolean>;
  loading: boolean;
}

const InvitationsDropdown: React.FC<InvitationsDropdownProps> = ({
  isOpen,
  onClose,
  invitations,
  onRevoke,
  onResend,
  loading,
}) => {
  const [actionLoading, setActionLoading] = useState<{ [key: string]: 'revoke' | 'resend' | null }>({});

  if (!isOpen) return null;

  const handleRevoke = async (invitationId: string) => {
    if (!invitationId || invitationId === 'undefined') {
      console.error("Invalid invitation ID:", invitationId);
      return;
    }

    setActionLoading(prev => ({ ...prev, [invitationId]: 'revoke' }));
    try {
      await onRevoke(invitationId);
    } finally {
      setActionLoading(prev => ({ ...prev, [invitationId]: null }));
    }
  };

  const handleResend = async (invitationId: string) => {
    if (!invitationId || invitationId === 'undefined') {
      console.error("Invalid invitation ID for resend:", invitationId);
      return;
    }

    setActionLoading(prev => ({ ...prev, [invitationId]: 'resend' }));
    try {
      await onResend(invitationId);
    } finally {
      setActionLoading(prev => ({ ...prev, [invitationId]: null }));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatRole = (role: string) => {
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 z-40" 
        onClick={onClose}
      />
      
      {/* Dropdown */}
      <div className="absolute top-full left-0 mt-2 w-96 bg-[#1F1F1F] border border-[#3D3D3D] rounded-lg shadow-lg z-50 max-h-96 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-[#3D3D3D]">
          <h3 className="text-white font-medium">Pending Invitations</h3>
          <button
            type="button"
            onClick={onClose}
            className="text-[#8A8A8A] cursor-pointer hover:text-white transition-colors"
            aria-label="Close invitations dropdown"
          >
            <IoMdClose size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="max-h-80 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center">
              <p className="text-[#A3A3A3]">Loading invitations...</p>
            </div>
          ) : invitations.length === 0 ? (
            <div className="p-4 text-center">
              <p className="text-[#A3A3A3]">No pending invitations</p>
            </div>
          ) : (
            <div className="divide-y divide-[#3D3D3D]">
              {invitations.map((invitation) => {
                const invitationId = invitation.id || invitation._id;
                if (!invitationId) return null;

                return (
                <div key={invitationId} className="p-4 hover:bg-[#2A2A2A] transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <p className="text-white font-medium truncate">
                          {invitation.email}
                        </p>
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-[#2E2E2E] text-[#A3A3A3]">
                          {formatRole(invitation.role)}
                        </span>
                      </div>
                      <p className="text-xs text-[#8A8A8A] mb-2">
                        Invited on {formatDate(invitation.createdAt)}
                      </p>
                      <p className="text-xs text-[#8A8A8A] mb-3 line-clamp-2">
                        {invitation.message}
                      </p>
                    </div>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2">
                    <button
                      type="button"
                      onClick={() => handleResend(invitationId)}
                      disabled={actionLoading[invitationId] === 'resend'}
                      className="flex cursor-pointer items-center space-x-1 px-3 py-1.5 bg-[#2E2E2E] hover:bg-[#3D3D3D] text-[#A3A3A3] hover:text-white text-xs rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <HiOutlineRefresh size={14} />
                      <span>
                        {actionLoading[invitationId] === 'resend' ? 'Resending...' : 'Resend'}
                      </span>
                    </button>
                    
                    <button
                      type="button"
                      onClick={() => handleRevoke(invitationId)}
                      disabled={actionLoading[invitationId] === 'revoke'}
                      className="flex cursor-pointer items-center space-x-1 px-3 py-1.5 bg-[#F04438] hover:bg-[#D92D20] text-white text-xs rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <MdDeleteOutline size={14} />
                      <span>
                        {actionLoading[invitationId] === 'revoke' ? 'Revoking...' : 'Revoke'}
                      </span>
                    </button>
                  </div>
                </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default InvitationsDropdown;
