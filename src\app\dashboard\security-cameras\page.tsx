"use client";
import React, { useState, useMemo } from "react";
import Image from "next/image";
import { HiOutlineArrowRight } from "react-icons/hi";
import { IconButton } from "@/components/button";
import ChooseSetup from "./choose-setup";
import CameraGrid from "@/components/sec-camera-comps/CameraGrid";
import { Camera } from "@/lib/cameras";
import { useCameras } from "@/hooks/useCameras";
import { useRoleAccess } from "@/hooks/useRoleAccess";

export default function SecurityCameras() {
  const { addedCameras, loading, error, refreshCameras } = useCameras();
  const { isAudit } = useRoleAccess();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<string>("All Locations");
  const [selectedCameraId, setSelectedCameraId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleCameraAdded = (
    // eslint-disable-next-line
    _camera: Camera
  ) => {
    closeModal();
  };

  const filteredCameras = useMemo(() => {
    return addedCameras.filter((camera) => {
      const matchesLocation = selectedLocation === "All Locations" || camera.name === selectedLocation;
      const matchesCameraId = selectedCameraId === "" || selectedCameraId === "All Cameras" || camera.id.toString() === selectedCameraId;
      const matchesSearch = !searchTerm ||
        camera.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        camera.id.toString().includes(searchTerm);
      return matchesLocation && matchesCameraId && matchesSearch;
    });
  }, [addedCameras, selectedLocation, selectedCameraId, searchTerm]);

  const locations = useMemo(() => {
    const uniqueLocations = Array.from(new Set(addedCameras.map((camera) => camera.name)));
    return ["All Locations", ...uniqueLocations];
  }, [addedCameras]);

  const cameraIds = useMemo(() => {
    return ["All Cameras", ...addedCameras.map((camera) => camera.id.toString())];
  }, [addedCameras]);

  if (loading && addedCameras.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh] text-center space-y-4">
        <div className="w-8 h-8 border-2 border-[#E4E7EC] border-t-transparent rounded-full animate-spin"></div>
        <p className="text-[#E4E7EC]">Loading cameras...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center text-white lg:p-6 py-6">
      {error && (
        <div className="mb-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg max-w-md w-full">
          <p className="text-red-400 text-sm">
            {error}. Please check your connection and try again.
          </p>
          <button
            type="button"
            onClick={refreshCameras}
            className="mt-2 text-red-300 hover:text-red-200 text-sm underline"
          >
            Retry
          </button>
        </div>
      )}

      {addedCameras.length === 0 ? (
        <div className="max-w-md mt-28 w-full flex flex-col items-center text-center">
          <div className="mb-6">
            <Image
              src="/sec-cam.svg"
              alt="Security Camera"
              width={160}
              height={160}
              className="w-40 h-40 sm:w-56 sm:h-56"
              priority
            />
          </div>

          <div className="text-[#E4E7EC]">
            {isAudit ? (
              <>
                <p className="text-sm sm:text-base">Camera access restricted.</p>
                <p className="text-sm sm:text-base">
                  Audit users do not have permission to view security cameras
                </p>
              </>
            ) : (
              <>
                <p className="text-sm sm:text-base">No Camera connected yet.</p>
                <p className="text-sm sm:text-base">
                  Let&apos;s set up your first camera for live monitoring
                </p>
              </>
            )}
          </div>
          {!isAudit && (
            <div className="mt-3">
              <IconButton
                label="Add Camera"
                icon={HiOutlineArrowRight}
                onClick={openModal}
              />
            </div>
          )}
        </div>
      ) : (
        <div className="">
          <CameraGrid
            cameras={filteredCameras}
            openModal={openModal}
            locations={locations}
            cameraIds={cameraIds}
            selectedLocation={selectedLocation}
            setSelectedLocation={setSelectedLocation}
            selectedCameraId={selectedCameraId}
            setSelectedCameraId={setSelectedCameraId}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            refreshCameras={refreshCameras}
          />
        </div>
      )}

      <ChooseSetup
        isOpen={isModalOpen}
        onClose={closeModal}
        onCameraAdded={handleCameraAdded}
        addedCameras={addedCameras}
      />
    </div>
  );
}
