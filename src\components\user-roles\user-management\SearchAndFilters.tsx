"use client";
import React, { useState } from "react";
import { CiSearch } from "react-icons/ci";
import { AiOutlinePlus } from "react-icons/ai";
import { SubmitButton } from "@/components/button";
import FilterDropdown from "./FilterDropdown";
import InvitationsDropdown from "./InvitationsDropdown";
import { ApiInvitation } from "@/utils/userServices";

interface SearchAndFiltersProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  pendingInvitations: number;
  pendingInvitationsList: ApiInvitation[];
  onAddUser: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
  onRevokeInvitation: (invitationId: string) => Promise<boolean>;
  onResendInvitation: (invitationId: string) => Promise<boolean>;
  loading: boolean;
  
  // Filter states
  selectedStatus: string;
  selectedUserType: string;
  selectedDateSort: string;
  
  // Filter dropdown states
  isDateDropdownOpen: boolean;
  isStatusDropdownOpen: boolean;
  isUserTypeDropdownOpen: boolean;
  
  // Filter handlers
  onStatusSelect: (status: string) => void;
  onUserTypeSelect: (userType: string) => void;
  onDateSortSelect: (dateSort: string) => void;
  
  // Dropdown toggle handlers
  onToggleDateDropdown: () => void;
  onToggleStatusDropdown: () => void;
  onToggleUserTypeDropdown: () => void;
}

const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({
  searchQuery,
  onSearchChange,
  pendingInvitations,
  pendingInvitationsList,
  onAddUser,
  isAuthenticated,
  isAdmin,
  onRevokeInvitation,
  onResendInvitation,
  loading,
  selectedStatus,
  selectedUserType,
  selectedDateSort,
  isDateDropdownOpen,
  isStatusDropdownOpen,
  isUserTypeDropdownOpen,
  onStatusSelect,
  onUserTypeSelect,
  onDateSortSelect,
  onToggleDateDropdown,
  onToggleStatusDropdown,
  onToggleUserTypeDropdown,
}) => {
  const [isInvitationsDropdownOpen, setIsInvitationsDropdownOpen] = useState(false);

  return (
    <>
      {/* Search and Add User Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-4 sm:space-y-0">
        {/* Search Input and Invitations */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-[#8A8A8A]">
              <CiSearch size={20} />
            </div>
            <input
              type="text"
              placeholder="Search users"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full sm:w-72 text-white py-3 pl-3 pr-4 border border-[#3D3D3D] focus:outline-none focus:ring-1 focus:ring-[#E4E7EC]"
            />
          </div>

          {/* Invitations Count - Only show for authenticated admin users */}
          {isAuthenticated && isAdmin && (
            <div className="flex items-center space-x-2 relative">
              <button
                type="button"
                onClick={() => setIsInvitationsDropdownOpen(!isInvitationsDropdownOpen)}
                className="bg-[#2E2E2E] border border-[#3D3D3D] px-3 py-2 flex items-center space-x-2 hover:bg-[#3D3D3D] transition-colors cursor-pointer"
              >
                <span className="text-sm text-[#E4E7EC]">Invitations</span>
                <div className="bg-[#F04438] text-white text-xs px-2 py-1 rounded-full min-w-[20px] text-center">
                  {pendingInvitations}
                </div>
              </button>

              <InvitationsDropdown
                isOpen={isInvitationsDropdownOpen}
                onClose={() => setIsInvitationsDropdownOpen(false)}
                invitations={pendingInvitationsList}
                onRevoke={onRevokeInvitation}
                onResend={onResendInvitation}
                loading={loading}
              />
            </div>
          )}
        </div>

        {/* Add User Button - Only show for admin users */}
        {isAdmin && (
          <div className="flex items-center">
            <SubmitButton
              label="Add User"
              type="button"
              onClick={onAddUser}
              icon={AiOutlinePlus}
              iconPosition="left"
              showIcon={true}
            />
          </div>
        )}
      </div>

      {/* Filters Section */}
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-sm text-[#8A8A8A]">Filters:</span>

        <FilterDropdown
          label="Date Added"
          isOpen={isDateDropdownOpen}
          onToggle={onToggleDateDropdown}
          options={["Newest First", "Oldest First"]}
          selectedValue={selectedDateSort}
          onSelect={onDateSortSelect}
        />

        <FilterDropdown
          label="Status"
          isOpen={isStatusDropdownOpen}
          onToggle={onToggleStatusDropdown}
          options={["Active", "Inactive"]}
          selectedValue={selectedStatus}
          onSelect={onStatusSelect}
        />

        <FilterDropdown
          label="User Type"
          isOpen={isUserTypeDropdownOpen}
          onToggle={onToggleUserTypeDropdown}
          options={["Admin", "User", "Security", "Audit"]}
          selectedValue={selectedUserType}
          onSelect={onUserTypeSelect}
        />
      </div>
    </>
  );
};

export default SearchAndFilters;
