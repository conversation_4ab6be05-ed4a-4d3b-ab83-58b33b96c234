import axios from "axios";
import Cookies from "js-cookie";

const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

if (!apiBaseUrl) {
  throw new Error("NEXT_PUBLIC_API_BASE_URL is not defined in .env.local");
}

const api = axios.create({
  baseURL: apiBaseUrl,
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use(
  (config) => {
    const token = Cookies.get("authToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Only log out user for authentication errors that are NOT related to camera operations
    if (error.response?.status === 401 || error.response?.status === 403) {
      const requestUrl = error.config?.url || '';

      // Camera-related endpoints that should NOT trigger logout (role-based restrictions expected)
      const cameraEndpoints = [
        '/cameras/manual',
        '/cameras/',
        '/cameras',
        '/check-connection',
        '/cameras/status',
        '/cameras/stats'
      ];

      // Check if this is a camera-related request
      const isCameraRequest = cameraEndpoints.some(endpoint =>
        requestUrl.includes(endpoint)
      );

      // Only log out if this is NOT a camera-related authentication error
      if (!isCameraRequest) {
        console.log("Logging out due to unexpected authentication failure on camera endpoint:", requestUrl);
        Cookies.remove("authToken");
        Cookies.remove("refreshToken");
        window.location.href = "/auth";
      } else {
        console.log("Ignoring 403 error for camera endpoint (role-based restriction):", requestUrl);
      }
    }
    return Promise.reject(error);
  }
);
export interface ApiCamera {
  _id: string;
  name: string;
  url: string;
  location: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  connectionStatus: string;
  connectionAttempts: number;
  lastConnectionError: string;
  connectivityStats: {
    uptimePercentage: number;
    lastOnline: string;
    averageResponseTime: number;
    monitoringSince: string;
  };
  settings: {
    thumbnailUrl: string;
    model: string;
    manufacturer: string;
  };
}

export interface CreateCameraData {
  name: string;
  url: string;
  location: string;
  isActive: boolean;
  settings: {
    manufacturer: string;
    model: string;
  };
}

export interface UpdateCameraData {
  name?: string;
  url?: string;
  location?: string;
  isActive?: boolean;
  settings?: {
    manufacturer?: string;
    model?: string;
  };
}

export interface ManualCameraData {
  name: string;
  ipAddress: string;
  username: string;
  manufacturer: string;
  model: string;
  password: string;
  port: number;
  location: string; // Required location field
}

export interface CameraStats {
  total: number;
  online: number;
  offline: number;
  unstable: number;
  unknown: number;
}

export interface ConnectionCheckResult {
  status: string;
  message: string;
}

export const cameraService = {
  // Get all cameras
  getAllCameras: async (): Promise<ApiCamera[]> => {
    const response = await api.get("/cameras");
    return response.data;
  },

  // Create new camera
  createCamera: async (data: CreateCameraData): Promise<ApiCamera> => {
    const response = await api.post("/cameras", data);
    return response.data;
  },

  // Get camera by ID
  getCameraById: async (id: string): Promise<ApiCamera> => {
    const response = await api.get(`/cameras/${id}`);
    return response.data;
  },

  // Update camera
  updateCamera: async (id: string, data: UpdateCameraData): Promise<ApiCamera> => {
    const response = await api.put(`/cameras/${id}`, data);
    return response.data;
  },

  // Delete camera
  deleteCamera: async (id: string): Promise<void> => {
    await api.delete(`/cameras/${id}`);
  },

  // Get cameras by status
  getCamerasByStatus: async (isActive?: boolean): Promise<ApiCamera[]> => {
    const params = isActive !== undefined ? { isActive } : {};
    const response = await api.get("/cameras/status", { params });
    return response.data;
  },

  // Get cameras by connection status
  getCamerasByConnectionStatus: async (status?: string): Promise<ApiCamera[]> => {
    const params = status ? { status } : {};
    const response = await api.get("/cameras/status/connection", { params });
    return response.data;
  },

  // Get camera statistics
  getCameraStats: async (): Promise<CameraStats> => {
    const response = await api.get("/cameras/stats");
    return response.data;
  },

  // Check camera connection
  checkCameraConnection: async (id: string): Promise<ConnectionCheckResult> => {
    const response = await api.post(`/cameras/${id}/check-connection`);
    return response.data;
  },

  // Add camera manually
  addCameraManually: async (data: ManualCameraData): Promise<ApiCamera> => {
    const response = await api.post("/cameras/manual", data);
    return response.data;
  },
};
