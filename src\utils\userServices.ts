import axios from "axios";
import Cookies from "js-cookie";

// Use the environment variable for API base URL
const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

// Check if the API is accessible
console.log("User service API base URL:", apiBaseUrl);

const api = axios.create({
  baseURL: apiBaseUrl,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get("authToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Only log out user for authentication errors that are NOT related to user management operations
    if (error.response?.status === 401 || error.response?.status === 403) {
      const requestUrl = error.config?.url || '';

      // Endpoints that should NOT trigger logout (expected 403 for certain roles)
      const protectedEndpoints = [
        '/invitations/invite',
        '/invitations',
        '/invitations/team-members',
        '/invitations/stats',
        '/users/',
        '/cameras' // Camera endpoints may be restricted for certain roles
      ];

      // Check if this is a protected endpoint that may have role-based restrictions
      const isProtectedEndpoint = protectedEndpoints.some(endpoint =>
        requestUrl.includes(endpoint)
      );

      // Only log out if this is NOT an expected role-based restriction
      if (!isProtectedEndpoint) {
        console.log("Logging out due to unexpected authentication failure on:", requestUrl);
        Cookies.remove("authToken");
        Cookies.remove("refreshToken");
        window.location.href = "/auth";
      } else {
        console.log("Ignoring 403 error for role-restricted endpoint:", requestUrl);
      }
    }
    return Promise.reject(error);
  }
);

// API Response Types
export interface ApiInvitation {
  id: string;
  _id?: string; // Keep for backward compatibility
  email: string;
  role: "user" | "admin" | "security" | "audit";
  invitedBy?: string;
  organizationName: string;
  message: string;
  status: "pending" | "accepted" | "expired" | "revoked";
  createdAt: string;
  expiresAt: string;
  isExpired: boolean;
}

export interface ApiTeamMember {
  id: string;
  _id?: string; // Keep as optional for backward compatibility
  name: string;
  email: string;
  role: "user" | "admin" | "security" | "audit";
  companyName: string;
  joinedAt: string;
  invitedAt: string;
}

export interface InvitationStats {
  result: {
    total: number;
    pending: number;
    accepted: number;
    expired: number;
    revoked: number;
    acceptanceRate: number;
  };
}

export interface InviteUserData {
  email: string;
  role: "user" | "admin" | "security" | "audit";
  organizationName: string;
  message: string;
  name: string;
}

export interface AcceptInvitationData {
  token: string;
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  companyName: string;
}

// User Services
export const userService = {
  // Send invitation to new user
  inviteUser: async (data: InviteUserData): Promise<{ message: string; result: ApiInvitation }> => {
    console.log("Sending invitation with data:", data);
    console.log("API URL:", `${apiBaseUrl}/invitations/invite`);
    try {
      const response = await api.post("/invitations/invite", data);
      console.log("Invitation response:", response.data);
      return response.data;
    } catch (error: unknown) {
      const err = error as { response?: { data?: unknown }; message?: string };
      console.error("Invitation API error:", err.response?.data || err.message);
      throw error;
    }
  },

  // Get all sent invitations with optional status filter
  getInvitations: async (status?: string, limit = 50, offset = 0): Promise<{ result: ApiInvitation[]; count: number }> => {
    const params: { limit: number; offset: number; status?: string } = { limit, offset };
    if (status) {
      params.status = status;
    }
    const response = await api.get("/invitations", { params });
    return response.data;
  },

  // Get invitation statistics
  getInvitationStats: async (): Promise<InvitationStats> => {
    const response = await api.get("/invitations/stats");
    return response.data;
  },

  // Validate invitation token (public endpoint)
  validateInvitationToken: async (token: string): Promise<{ message: string; result: unknown }> => {
    const response = await axios.get(`${apiBaseUrl}/invitations/validate/${token}`);
    return response.data;
  },

  // Accept invitation and create account (public endpoint)
  acceptInvitation: async (data: AcceptInvitationData): Promise<{ message: string; result: unknown }> => {
    const response = await axios.post(`${apiBaseUrl}/invitations/accept`, data);
    return response.data;
  },

  // Revoke pending invitation
  revokeInvitation: async (invitationId: string): Promise<{ message: string; result: unknown }> => {
    const response = await api.post(`/invitations/${invitationId}/revoke`);
    return response.data;
  },

  // Resend invitation
  resendInvitation: async (invitationId: string, message?: string): Promise<{ message: string; result: unknown }> => {
    const data = message ? { message } : {};
    const response = await api.post(`/invitations/${invitationId}/resend`, data);
    return response.data;
  },

  // Get team members (accepted invitations)
  getTeamMembers: async (limit = 50, offset = 0): Promise<{ result: ApiTeamMember[]; count: number }> => {
    const response = await api.get("/invitations/team-members", {
      params: { limit, offset }
    });
    return response.data;
  },

  // Remove team member
  removeTeamMember: async (userId: string): Promise<{ message: string }> => {
    const response = await api.delete(`/invitations/team-members/${userId}`);
    return response.data;
  },

  // Update user information
  updateUser: async (userId: string, userData: {
    name: string;
    email: string;
    role?: string;
  }): Promise<{ message: string; result: unknown }> => {
    console.log("Updating user with data:", userData);
    console.log("API URL:", `${apiBaseUrl}/users/${userId}`);
    try {
      const response = await api.put(`/users/${userId}`, userData);
      console.log("Update user response:", response.data);
      return response.data;
    } catch (error: unknown) {
      const err = error as { response?: { data?: unknown }; message?: string };
      console.error("Update user API error:", err.response?.data || err.message);
      throw error;
    }
  },
};
